#!/usr/bin/env node

/**
 * Midjourney 集成设置脚本
 * 
 * 此脚本帮助用户配置 Midjourney API 集成
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

async function setupMidjourney() {
  console.log('🎨 Midjourney API 集成设置向导');
  console.log('=====================================\n');
  
  try {
    // 检查 .env 文件
    const envPath = path.join(__dirname, '../.env');
    let envContent = '';
    
    if (fs.existsSync(envPath)) {
      envContent = fs.readFileSync(envPath, 'utf8');
      console.log('✅ 找到现有的 .env 文件');
    } else {
      console.log('📝 将创建新的 .env 文件');
    }
    
    // 获取用户输入
    console.log('\n请提供以下信息：\n');
    
    const baseUrl = await question('Midjourney API Base URL (默认: https://api.gpt-best.com): ') || 'https://api.gpt-best.com';
    const apiKey = await question('Midjourney API Key: ');
    
    if (!apiKey) {
      console.log('❌ API Key 是必需的，请重新运行脚本');
      rl.close();
      return;
    }
    
    // 更新或创建 .env 文件
    const midjourneyConfig = `
# Midjourney API 配置
MIDJOURNEY_BASE_URL=${baseUrl}
MIDJOURNEY_API_KEY=${apiKey}
`;
    
    // 检查是否已存在 Midjourney 配置
    if (envContent.includes('MIDJOURNEY_BASE_URL') || envContent.includes('MIDJOURNEY_API_KEY')) {
      console.log('\n⚠️  检测到现有的 Midjourney 配置');
      const overwrite = await question('是否覆盖现有配置？ (y/N): ');
      
      if (overwrite.toLowerCase() === 'y' || overwrite.toLowerCase() === 'yes') {
        // 移除现有的 Midjourney 配置
        envContent = envContent.replace(/# Midjourney API 配置[\s\S]*?(?=\n#|\n[A-Z]|$)/g, '');
        envContent = envContent.replace(/MIDJOURNEY_BASE_URL=.*\n?/g, '');
        envContent = envContent.replace(/MIDJOURNEY_API_KEY=.*\n?/g, '');
      } else {
        console.log('❌ 取消配置更新');
        rl.close();
        return;
      }
    }
    
    // 添加新配置
    const newEnvContent = envContent.trim() + midjourneyConfig;
    
    // 写入文件
    fs.writeFileSync(envPath, newEnvContent);
    console.log('\n✅ 配置已保存到 .env 文件');
    
    // 测试配置
    console.log('\n🧪 测试 API 连接...');
    
    // 设置环境变量
    process.env.MIDJOURNEY_BASE_URL = baseUrl;
    process.env.MIDJOURNEY_API_KEY = apiKey;
    
    const AiHuituService = require('../services/aiHuituService');
    const service = new AiHuituService();
    
    try {
      // 简单的 API 测试
      console.log('正在测试 API 连接...');
      
      // 这里可以添加一个简单的 API 测试调用
      console.log('✅ API 配置看起来正确');
      console.log('\n🎉 Midjourney 集成设置完成！');
      console.log('\n下一步：');
      console.log('1. 重启应用服务器');
      console.log('2. 在前端选择 Midjourney 服务');
      console.log('3. 开始生成图片！');
      
    } catch (error) {
      console.log('❌ API 测试失败:', error.message);
      console.log('请检查 API Key 是否正确');
    }
    
  } catch (error) {
    console.error('❌ 设置过程中发生错误:', error.message);
  } finally {
    rl.close();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  setupMidjourney().catch(console.error);
}

module.exports = { setupMidjourney };
