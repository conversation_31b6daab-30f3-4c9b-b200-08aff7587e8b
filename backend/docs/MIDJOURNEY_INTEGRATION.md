# Midjourney API 集成文档

## 概述

本项目已集成 Midjourney API，支持通过 API 调用进行图像生成、图像混合、图像描述等功能。

## 配置

### 1. 环境变量配置

在 `.env` 文件中添加以下配置：

```bash
# Midjourney API 配置
MIDJOURNEY_BASE_URL=https://api.gpt-best.com
MIDJOURNEY_API_KEY=sk-your-midjourney-api-key-here
```

### 2. 获取 API Key

1. 访问 [API 文档](https://gpt-best.apifox.cn/doc-6172523)
2. 注册账号并获取 API Key
3. 将 API Key 配置到环境变量中

## 功能特性

### 1. 文生图 (Imagine)
- 支持自然语言描述生成图像
- 支持多种参数配置（版本、宽高比、质量等）
- 自动轮询任务状态直到完成
- 自动下载图片到本地

### 2. 图生图 (Blend)
- 支持多张图片混合
- 支持不同的混合比例
- 支持自定义输出尺寸

### 3. 图生文 (Describe)
- 分析图片内容并生成描述
- 支持多种语言输出

### 4. 动作执行 (Action)
- 支持图片放大 (Upscale)
- 支持图片变体 (Variation)
- 支持重新生成 (Reroll)
- 支持缩放 (Zoom)

## API 接口

### 1. 生成图片
```http
POST /api/midjourney/generate
Content-Type: application/json

{
  "prompt": "a cute cat sitting on a chair, digital art",
  "options": {
    "version": "6.1",
    "aspect_ratio": "1:1",
    "quality": "1"
  }
}
```

### 2. 图片混合
```http
POST /api/midjourney/blend
Content-Type: application/json

{
  "base64Array": [
    "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
    "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA..."
  ],
  "options": {
    "dimensions": "SQUARE"
  }
}
```

### 3. 图片描述
```http
POST /api/midjourney/describe
Content-Type: application/json

{
  "base64": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA..."
}
```

### 4. 执行动作
```http
POST /api/midjourney/action
Content-Type: application/json

{
  "customId": "MJ::JOB::upsample::1::message-hash",
  "taskId": "1234567890"
}
```

### 5. 查询任务状态
```http
GET /api/midjourney/task/{taskId}
```

### 6. 获取历史记录
```http
GET /api/midjourney/history?limit=50
```

## 参数说明

### 生成参数
- `version`: Midjourney 版本 (如: "6.1", "5.2")
- `aspect_ratio`: 宽高比 (如: "1:1", "16:9", "9:16")
- `quality`: 质量等级 (如: "0.25", "0.5", "1", "2")
- `stylize`: 风格化程度 (0-1000)
- `chaos`: 随机性 (0-100)

### 混合参数
- `dimensions`: 输出尺寸
  - `PORTRAIT`: 2:3 比例
  - `SQUARE`: 1:1 比例
  - `LANDSCAPE`: 3:2 比例

## 测试

运行测试脚本验证配置：

```bash
cd backend
node test/test-midjourney.js
```

## 错误处理

常见错误及解决方案：

1. **API Key 无效**
   - 检查 API Key 是否正确配置
   - 确认 API Key 是否有效且有足够余额

2. **任务超时**
   - Midjourney 生成通常需要 1-5 分钟
   - 检查网络连接是否稳定

3. **图片下载失败**
   - 检查本地存储目录权限
   - 确认网络可以访问图片 URL

## 注意事项

1. **API 限制**
   - 遵守 API 调用频率限制
   - 注意账户余额和使用配额

2. **图片存储**
   - 生成的图片会自动下载到本地
   - 定期清理存储空间

3. **安全性**
   - 不要在代码中硬编码 API Key
   - 使用环境变量管理敏感信息

## 更新日志

- **v1.0.0**: 初始版本，支持基本的文生图功能
- **v1.1.0**: 添加图生图、图生文功能
- **v1.2.0**: 添加动作执行和任务查询功能
