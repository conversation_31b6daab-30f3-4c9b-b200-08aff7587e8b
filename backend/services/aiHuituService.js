/**
 * AI绘图服务 - 基于 Midjourney API
 *
 * 功能说明：
 * - 使用 Midjourney API 进行图像生成
 * - 支持文生图、图生图、图生文等功能
 * - 轮询任务状态直到完成
 * - 下载图片到本地
 */

const axios = require('axios');
const fs = require('fs').promises;
const path = require('path');

class AiHuituService {
  constructor() {
    // Midjourney API 配置
    this.baseUrl = process.env.MIDJOURNEY_BASE_URL || 'https://api.gpt-best.com';
    this.apiKey = process.env.MIDJOURNEY_API_KEY || 'JEN1A9jQj7mBeWSP5lMUpTvSHaZ3lw==';
    this.mode = 'fast'; // fast 或 relax 模式

    // 工作目录
    this.workingDir = path.join(__dirname, '../aihuitu_storage');
    this.imagesDir = path.join(this.workingDir, 'images');

    this.initializeDirectories();
  }

  // 初始化目录
  async initializeDirectories() {
    try {
      await fs.mkdir(this.workingDir, { recursive: true });
      await fs.mkdir(this.imagesDir, { recursive: true });
      console.log('📁 AI绘图目录初始化成功');
    } catch (error) {
      console.error('❌ 创建AI绘图目录失败:', error);
    }
  }

  // 获取请求头
  getHeaders() {
    return {
      'Authorization': `Bearer ${this.apiKey}`,
      'Content-Type': 'application/json',
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    };
  }

  // 获取 API 端点 URL
  getApiUrl(endpoint) {
    return `${this.baseUrl}/${this.mode}${endpoint}`;
  }

  // 查询任务状态
  async getTaskStatus(taskId) {
    try {
      console.log(`🔍 查询任务状态: ${taskId}`);

      const response = await axios.get(this.getApiUrl(`/mj/task/${taskId}/fetch`), {
        headers: this.getHeaders()
      });

      return response.data;

    } catch (error) {
      console.error('❌ 查询任务状态失败:', error.message);
      throw error;
    }
  }

  // 轮询等待任务完成
  async pollForCompletion(taskId, timeout = 300000) { // 5分钟超时
    const startTime = Date.now();
    const pollInterval = 5000; // 5秒轮询一次

    console.log(`⏳ 开始轮询任务状态 [${taskId}]...`);

    while (Date.now() - startTime < timeout) {
      try {
        const taskData = await this.getTaskStatus(taskId);

        console.log(`📊 任务状态 [${taskId}]: ${taskData.status} - ${taskData.progress || '0%'}`);

        if (taskData.status === 'SUCCESS') {
          // 下载图片
          if (taskData.imageUrl) {
            await this.downloadTaskImages(taskData);
          }
          return taskData;
        } else if (taskData.status === 'FAILURE') {
          throw new Error(taskData.failReason || '任务生成失败');
        }

        // 等待下次轮询
        await new Promise(resolve => setTimeout(resolve, pollInterval));

      } catch (error) {
        console.error('❌ 轮询任务状态失败:', error.message);
        // 继续轮询，除非是致命错误
        if (error.response && error.response.status === 401) {
          throw new Error('API Key 无效或已过期');
        }
        await new Promise(resolve => setTimeout(resolve, pollInterval));
      }
    }

    throw new Error('任务超时');
  }

  // 下载任务图片
  async downloadTaskImages(taskData) {
    try {
      // Midjourney API 返回的是单个图片 URL
      const imageUrl = taskData.imageUrl;
      const downloadedImages = [];

      console.log(`📥 开始下载图片: ${imageUrl}`);

      if (imageUrl) {
        const filename = `${taskData.id}.png`;
        const localPath = await this.downloadImage(imageUrl, filename);
        if (localPath) {
          downloadedImages.push({
            url: imageUrl,
            localPath: localPath,
            filename: filename
          });
        }
      }

      console.log(`✅ 成功下载 ${downloadedImages.length} 张图片`);
      taskData.downloadedImages = downloadedImages;

    } catch (error) {
      console.error('❌ 下载图片失败:', error);
    }
  }

  // 下载单张图片
  async downloadImage(imageUrl, filename) {
    try {
      const response = await axios.get(imageUrl, {
        responseType: 'stream',
        timeout: 30000
      });
      
      const localPath = path.join(this.imagesDir, filename);
      const writer = require('fs').createWriteStream(localPath);
      
      response.data.pipe(writer);
      
      return new Promise((resolve, reject) => {
        writer.on('finish', () => {
          console.log(`✅ 图片下载成功: ${filename}`);
          resolve(localPath);
        });
        writer.on('error', reject);
      });
      
    } catch (error) {
      console.error(`❌ 下载图片失败 [${filename}]:`, error.message);
      return null;
    }
  }

  // 提交 Imagine 任务（文生图）
  async submitImagineTask(prompt, options = {}) {
    try {
      console.log('🎨 提交 Imagine 任务:', prompt);

      // 构建完整的prompt
      let fullPrompt = prompt;

      // 添加版本参数
      if (options.version) {
        fullPrompt += ` --v ${options.version}`;
      } else {
        fullPrompt += ' --v 6.1'; // 默认版本
      }

      // 添加宽高比
      if (options.aspect_ratio) {
        fullPrompt += ` --ar ${options.aspect_ratio}`;
      }

      // 添加质量参数
      if (options.quality) {
        fullPrompt += ` --q ${options.quality}`;
      }

      console.log('📝 完整提示词:', fullPrompt);

      const requestData = {
        prompt: fullPrompt,
        base64Array: options.base64Array || []
      };

      const response = await axios.post(this.getApiUrl('/mj/submit/imagine'), requestData, {
        headers: this.getHeaders()
      });

      if (response.data && response.data.code === 1) {
        console.log('✅ Imagine 任务提交成功');
        return {
          success: true,
          taskId: response.data.result,
          prompt: prompt,
          fullPrompt: fullPrompt,
          options: options
        };
      } else {
        console.error('❌ Imagine 任务提交失败:', response.data);
        return {
          success: false,
          error: response.data.description || '任务提交失败'
        };
      }

    } catch (error) {
      console.error('❌ 提交 Imagine 任务异常:', error.message);
      return {
        success: false,
        error: error.message
      };
    }
  }



  // 提交 Blend 任务（图生图）
  async submitBlendTask(base64Array, options = {}) {
    try {
      console.log('🎨 提交 Blend 任务');

      const requestData = {
        base64Array: base64Array,
        dimensions: options.dimensions || 'SQUARE' // PORTRAIT(2:3); SQUARE(1:1); LANDSCAPE(3:2)
      };

      const response = await axios.post(this.getApiUrl('/mj/submit/blend'), requestData, {
        headers: this.getHeaders()
      });

      if (response.data && response.data.code === 1) {
        console.log('✅ Blend 任务提交成功');
        return {
          success: true,
          taskId: response.data.result,
          options: options
        };
      } else {
        console.error('❌ Blend 任务提交失败:', response.data);
        return {
          success: false,
          error: response.data.description || '任务提交失败'
        };
      }

    } catch (error) {
      console.error('❌ 提交 Blend 任务异常:', error.message);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // 提交 Describe 任务（图生文）
  async submitDescribeTask(base64) {
    try {
      console.log('🎨 提交 Describe 任务');

      const requestData = {
        base64: base64
      };

      const response = await axios.post(this.getApiUrl('/mj/submit/describe'), requestData, {
        headers: this.getHeaders()
      });

      if (response.data && response.data.code === 1) {
        console.log('✅ Describe 任务提交成功');
        return {
          success: true,
          taskId: response.data.result
        };
      } else {
        console.error('❌ Describe 任务提交失败:', response.data);
        return {
          success: false,
          error: response.data.description || '任务提交失败'
        };
      }

    } catch (error) {
      console.error('❌ 提交 Describe 任务异常:', error.message);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // 执行动作（按钮操作）
  async submitAction(customId, taskId) {
    try {
      console.log('🎨 执行动作:', customId);

      const requestData = {
        customId: customId,
        taskId: taskId
      };

      const response = await axios.post(this.getApiUrl('/mj/submit/action'), requestData, {
        headers: this.getHeaders()
      });

      if (response.data && response.data.code === 1) {
        console.log('✅ 动作执行成功');
        return {
          success: true,
          taskId: response.data.result
        };
      } else {
        console.error('❌ 动作执行失败:', response.data);
        return {
          success: false,
          error: response.data.description || '动作执行失败'
        };
      }

    } catch (error) {
      console.error('❌ 执行动作异常:', error.message);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // 生成图片（完整流程）
  async generateImage(prompt, options = {}) {
    try {
      console.log('🚀 开始 Midjourney 绘图流程...');

      // 提交任务
      const submitResult = await this.submitImagineTask(prompt, options);
      if (!submitResult.success) {
        return submitResult;
      }

      console.log('⏳ 等待任务完成...');

      // 使用轮询等待任务完成
      const taskData = await this.pollForCompletion(submitResult.taskId);

      // 保存到历史记录
      await this.saveToHistory({
        id: submitResult.taskId,
        timestamp: new Date().toISOString(),
        prompt: prompt,
        fullPrompt: submitResult.fullPrompt,
        options: options,
        result: taskData,
        success: true
      });

      return {
        success: true,
        taskId: submitResult.taskId,
        prompt: prompt,
        fullPrompt: submitResult.fullPrompt,
        images: taskData.downloadedImages || [],
        imageUrls: taskData.imageUrl ? [taskData.imageUrl] : [],
        buttons: taskData.buttons || [],
        progress: taskData.progress,
        finishTime: taskData.finishTime,
        status: taskData.status
      };

    } catch (error) {
      console.error('❌ Midjourney 绘图失败:', error.message);

      // 保存失败记录
      await this.saveToHistory({
        id: Date.now().toString(),
        timestamp: new Date().toISOString(),
        prompt: prompt,
        options: options,
        error: error.message,
        success: false
      });

      return {
        success: false,
        error: error.message
      };
    }
  }

  // 保存历史记录
  async saveToHistory(record) {
    try {
      const historyFile = path.join(this.workingDir, 'generation_history.json');

      let history = [];
      try {
        const data = await fs.readFile(historyFile, 'utf8');
        history = JSON.parse(data);
      } catch (error) {
        // 文件不存在，创建新的历史记录
      }

      history.unshift(record);

      // 保留最近1000条记录
      if (history.length > 1000) {
        history = history.slice(0, 1000);
      }

      await fs.writeFile(historyFile, JSON.stringify(history, null, 2));

    } catch (error) {
      console.error('❌ 保存历史记录失败:', error);
    }
  }

  // 获取历史记录
  async getHistory(limit = 50) {
    try {
      const historyFile = path.join(this.workingDir, 'generation_history.json');
      const data = await fs.readFile(historyFile, 'utf8');
      const history = JSON.parse(data);

      return {
        success: true,
        history: history.slice(0, limit),
        total: history.length
      };

    } catch (error) {
      return {
        success: true,
        history: [],
        total: 0
      };
    }
  }

  // 图生图（Blend）
  async blendImages(base64Array, options = {}) {
    try {
      console.log('🚀 开始 Midjourney Blend 流程...');

      // 提交任务
      const submitResult = await this.submitBlendTask(base64Array, options);
      if (!submitResult.success) {
        return submitResult;
      }

      console.log('⏳ 等待任务完成...');

      // 使用轮询等待任务完成
      const taskData = await this.pollForCompletion(submitResult.taskId);

      return {
        success: true,
        taskId: submitResult.taskId,
        images: taskData.downloadedImages || [],
        imageUrls: taskData.imageUrl ? [taskData.imageUrl] : [],
        buttons: taskData.buttons || [],
        progress: taskData.progress,
        finishTime: taskData.finishTime,
        status: taskData.status
      };

    } catch (error) {
      console.error('❌ Midjourney Blend 失败:', error.message);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // 图生文（Describe）
  async describeImage(base64) {
    try {
      console.log('🚀 开始 Midjourney Describe 流程...');

      // 提交任务
      const submitResult = await this.submitDescribeTask(base64);
      if (!submitResult.success) {
        return submitResult;
      }

      console.log('⏳ 等待任务完成...');

      // 使用轮询等待任务完成
      const taskData = await this.pollForCompletion(submitResult.taskId);

      return {
        success: true,
        taskId: submitResult.taskId,
        description: taskData.prompt || taskData.promptEn,
        progress: taskData.progress,
        finishTime: taskData.finishTime,
        status: taskData.status
      };

    } catch (error) {
      console.error('❌ Midjourney Describe 失败:', error.message);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // 关闭连接
  async close() {
    console.log('🔌 Midjourney 服务已关闭');
  }
}

module.exports = AiHuituService;
