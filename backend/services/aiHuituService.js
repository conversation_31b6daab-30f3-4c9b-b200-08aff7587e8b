/**
 * AI绘图服务 - 基于 aihuitu.spbst.cn
 * 
 * 功能说明：
 * - 模拟登录获取token
 * - 提交生图任务
 * - WebSocket监听任务状态
 * - 下载图片到本地
 */

const axios = require('axios');
const fs = require('fs').promises;
const path = require('path');
const crypto = require('crypto');

class AiHuituService {
  constructor() {
    this.baseUrl = 'https://aihuitu.spbst.cn';
    this.credentials = {
      email: 'AROJRC-4-11001',
      password: 'ooitmofvuq',
      domain:'aihuitu.spbst.cn'
    };

    // 从研究文档中的固定值
    this.channelId = 'b37e6574b42b47ec97a1bec1b430ee07';
    this.uid = '9ccc09287e5f4817b1bad8ebeb26876e';

    this.token = null;

    // 工作目录
    this.workingDir = path.join(__dirname, '../aihuitu_storage');
    this.imagesDir = path.join(this.workingDir, 'images');

    this.initializeDirectories();
  }

  // 初始化目录
  async initializeDirectories() {
    try {
      await fs.mkdir(this.workingDir, { recursive: true });
      await fs.mkdir(this.imagesDir, { recursive: true });
      console.log('📁 AI绘图目录初始化成功');
    } catch (error) {
      console.error('❌ 创建AI绘图目录失败:', error);
    }
  }

  // 登录获取token
  async login() {
    try {
      console.log('🔐 正在登录AI绘图服务...');
      
      const response = await axios.post(`${this.baseUrl}/api/app/discord/user/login`, {
        username: this.credentials.username,
        password: this.credentials.password
      }, {
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      });

      if (response.data && response.data.code === 1000) {
        this.token = response.data.data.token;
        console.log('✅ AI绘图服务登录成功');
        return true;
      } else {
        console.error('❌ 登录失败:', response.data);
        return false;
      }
    } catch (error) {
      console.error('❌ 登录异常:', error.message);
      return false;
    }
  }

  // 确保已登录
  async ensureLoggedIn() {
    if (!this.token) {
      const success = await this.login();
      if (!success) {
        throw new Error('登录失败，无法使用AI绘图服务');
      }
    }
    return true;
  }

  // 查询任务状态
  async getTaskStatus(taskId) {
    try {
      await this.ensureLoggedIn();

      const response = await axios.get(`${this.baseUrl}/api/app/discord/user/task/${taskId}`, {
        headers: {
          'Authorization': this.token,
          'Content-Type': 'application/json',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      });

      if (response.data && response.data.code === 1000) {
        return response.data.data;
      } else {
        throw new Error(response.data.message || '查询任务状态失败');
      }

    } catch (error) {
      console.error('❌ 查询任务状态失败:', error.message);
      throw error;
    }
  }

  // 轮询等待任务完成
  async pollForCompletion(taskId, timeout = 300000) { // 5分钟超时
    const startTime = Date.now();
    const pollInterval = 5000; // 5秒轮询一次

    console.log(`⏳ 开始轮询任务状态 [${taskId}]...`);

    while (Date.now() - startTime < timeout) {
      try {
        const taskData = await this.getTaskStatus(taskId);

        console.log(`📊 任务状态 [${taskId}]: ${taskData.status} - ${taskData.progress || '0%'}`);

        if (taskData.status === '已完成') {
          // 下载图片
          if (taskData.imageUrl) {
            await this.downloadTaskImages(taskData);
          }
          return taskData;
        } else if (taskData.status === '失败') {
          throw new Error('任务生成失败');
        }

        // 等待下次轮询
        await new Promise(resolve => setTimeout(resolve, pollInterval));

      } catch (error) {
        console.error('❌ 轮询任务状态失败:', error.message);
        // 继续轮询，除非是致命错误
        if (error.message.includes('登录') || error.message.includes('token')) {
          throw error;
        }
        await new Promise(resolve => setTimeout(resolve, pollInterval));
      }
    }

    throw new Error('任务超时');
  }

  // 下载任务图片
  async downloadTaskImages(taskData) {
    try {
      const imageUrls = taskData.imageUrl.split(',');
      const downloadedImages = [];
      
      console.log(`📥 开始下载 ${imageUrls.length} 张图片...`);
      
      for (let i = 0; i < imageUrls.length; i++) {
        const imageUrl = imageUrls[i].trim();
        if (imageUrl) {
          const filename = `${taskData.id}_${i}.png`;
          const localPath = await this.downloadImage(imageUrl, filename);
          if (localPath) {
            downloadedImages.push({
              url: imageUrl,
              localPath: localPath,
              filename: filename
            });
          }
        }
      }
      
      console.log(`✅ 成功下载 ${downloadedImages.length} 张图片`);
      taskData.downloadedImages = downloadedImages;
      
    } catch (error) {
      console.error('❌ 下载图片失败:', error);
    }
  }

  // 下载单张图片
  async downloadImage(imageUrl, filename) {
    try {
      const response = await axios.get(imageUrl, {
        responseType: 'stream',
        timeout: 30000
      });
      
      const localPath = path.join(this.imagesDir, filename);
      const writer = require('fs').createWriteStream(localPath);
      
      response.data.pipe(writer);
      
      return new Promise((resolve, reject) => {
        writer.on('finish', () => {
          console.log(`✅ 图片下载成功: ${filename}`);
          resolve(localPath);
        });
        writer.on('error', reject);
      });
      
    } catch (error) {
      console.error(`❌ 下载图片失败 [${filename}]:`, error.message);
      return null;
    }
  }

  // 提交生图任务
  async submitTask(prompt, options = {}) {
    try {
      await this.ensureLoggedIn();

      // 确保WebSocket连接
      if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
        await this.connectWebSocket();
        // 等待连接稳定
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      console.log('🎨 提交生图任务:', prompt);

      // 构建完整的prompt
      let fullPrompt = prompt;

      // 添加版本参数
      if (options.version) {
        fullPrompt += ` --v ${options.version}`;
      } else {
        fullPrompt += ' --v 6.1'; // 默认版本
      }

      // 添加速度参数
      if (options.fast !== false) {
        fullPrompt += ' --fast';
      }

      // 添加宽高比
      if (options.aspect_ratio) {
        fullPrompt += ` --ar ${options.aspect_ratio}`;
      }

      // 添加质量参数
      if (options.quality) {
        fullPrompt += ` --q ${options.quality}`;
      }

      console.log('📝 完整提示词:', fullPrompt);

      const response = await axios.post(`${this.baseUrl}/api/app/discord/user/submitTask`, {
        action: 'IMAGINE',
        botType: 'MID_JOURNEY',
        channelId: this.channelId,
        prompt: fullPrompt
      }, {
        headers: {
          'Authorization': this.token,
          'Content-Type': 'application/json',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      });

      if (response.data && response.data.code === 1000) {
        console.log('✅ 任务提交成功');
        return {
          success: true,
          taskId: response.data.data,
          prompt: prompt,
          fullPrompt: fullPrompt,
          options: options
        };
      } else {
        console.error('❌ 任务提交失败:', response.data);
        return {
          success: false,
          error: response.data.message || '任务提交失败'
        };
      }

    } catch (error) {
      console.error('❌ 提交任务异常:', error.message);
      return {
        success: false,
        error: error.message
      };
    }
  }



  // 生成图片（完整流程）
  async generateImage(prompt, options = {}) {
    try {
      console.log('🚀 开始AI绘图流程...');

      // 提交任务
      const submitResult = await this.submitTask(prompt, options);
      if (!submitResult.success) {
        return submitResult;
      }

      console.log('⏳ 等待任务完成...');

      // 使用轮询等待任务完成
      const taskData = await this.pollForCompletion(submitResult.taskId);

      // 保存到历史记录
      await this.saveToHistory({
        id: submitResult.taskId,
        timestamp: new Date().toISOString(),
        prompt: prompt,
        fullPrompt: submitResult.fullPrompt,
        options: options,
        result: taskData,
        success: true
      });

      return {
        success: true,
        taskId: submitResult.taskId,
        prompt: prompt,
        fullPrompt: submitResult.fullPrompt,
        images: taskData.downloadedImages || [],
        imageUrls: taskData.imageUrl ? taskData.imageUrl.split(',') : [],
        buttons: taskData.buttons ? JSON.parse(taskData.buttons) : [],
        progress: taskData.progress,
        finishTime: taskData.finishTime,
        jobId: taskData.jobId
      };

    } catch (error) {
      console.error('❌ AI绘图失败:', error.message);

      // 保存失败记录
      await this.saveToHistory({
        id: Date.now().toString(),
        timestamp: new Date().toISOString(),
        prompt: prompt,
        options: options,
        error: error.message,
        success: false
      });

      return {
        success: false,
        error: error.message
      };
    }
  }

  // 保存历史记录
  async saveToHistory(record) {
    try {
      const historyFile = path.join(this.workingDir, 'generation_history.json');

      let history = [];
      try {
        const data = await fs.readFile(historyFile, 'utf8');
        history = JSON.parse(data);
      } catch (error) {
        // 文件不存在，创建新的历史记录
      }

      history.unshift(record);

      // 保留最近1000条记录
      if (history.length > 1000) {
        history = history.slice(0, 1000);
      }

      await fs.writeFile(historyFile, JSON.stringify(history, null, 2));

    } catch (error) {
      console.error('❌ 保存历史记录失败:', error);
    }
  }

  // 获取历史记录
  async getHistory(limit = 50) {
    try {
      const historyFile = path.join(this.workingDir, 'generation_history.json');
      const data = await fs.readFile(historyFile, 'utf8');
      const history = JSON.parse(data);

      return {
        success: true,
        history: history.slice(0, limit),
        total: history.length
      };

    } catch (error) {
      return {
        success: true,
        history: [],
        total: 0
      };
    }
  }

  // 关闭连接
  async close() {
    console.log('🔌 AI绘图服务已关闭');
  }
}

module.exports = AiHuituService;
