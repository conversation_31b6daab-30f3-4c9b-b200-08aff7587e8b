const express = require('express');
const router = express.Router();
const AiHuituService = require('../services/aiHuituService');

// 创建Midjourney服务实例（使用更新后的 aiHuituService）
const midjourneyService = new AiHuituService();

// 检查Midjourney服务状态
router.get('/status', async (req, res) => {
  try {
    // 检查 API Key 和配置
    const hasApiKey = !!process.env.MIDJOURNEY_API_KEY && process.env.MIDJOURNEY_API_KEY !== 'sk-your-midjourney-api-key-here';
    const hasBaseUrl = !!process.env.MIDJOURNEY_BASE_URL;

    const status = {
      available: hasApiKey && hasBaseUrl,
      configured: hasApiKey && hasBaseUrl,
      config: {
        hasApiKey,
        hasBaseUrl,
        baseUrl: process.env.MIDJOURNEY_BASE_URL || 'https://api.gpt-best.com'
      },
      message: hasApiKey && hasBaseUrl ? 'Midjourney API 配置完整' : '需要配置 Midjourney API Key'
    };

    res.json({
      success: true,
      status,
      service: 'midjourney'
    });
  } catch (error) {
    console.error('❌ 检查Midjourney状态失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 生成图片
router.post('/generate', async (req, res) => {
  try {
    const { prompt, options = {} } = req.body;

    if (!prompt) {
      return res.status(400).json({
        success: false,
        error: '提示词不能为空'
      });
    }

    console.log('🎨 开始Midjourney图片生成:', prompt);

    // 调用Midjourney API生成图片
    const result = await midjourneyService.generateImage(prompt, options);

    if (result.success) {
      res.json({
        success: true,
        message: 'Midjourney图片生成完成',
        data: {
          taskId: result.taskId,
          prompt: result.prompt,
          fullPrompt: result.fullPrompt,
          images: result.images,
          imageUrls: result.imageUrls,
          buttons: result.buttons,
          finishTime: result.finishTime,
          status: result.status
        }
      });
    } else {
      res.status(500).json({
        success: false,
        error: result.error || 'Midjourney图片生成失败'
      });
    }

  } catch (error) {
    console.error('❌ Midjourney生成请求失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 执行动作 (upscale, variation, reroll 等)
router.post('/action', async (req, res) => {
  try {
    const { customId, taskId } = req.body;

    if (!customId || !taskId) {
      return res.status(400).json({
        success: false,
        error: '缺少必要参数: customId, taskId'
      });
    }

    console.log(`🔄 开始执行Midjourney动作: ${customId}`);

    // 执行动作
    const result = await midjourneyService.submitAction(customId, taskId);

    if (result.success) {
      // 等待新任务完成
      const taskData = await midjourneyService.pollForCompletion(result.taskId);

      res.json({
        success: true,
        message: 'Midjourney动作执行完成',
        data: {
          taskId: result.taskId,
          images: taskData.downloadedImages || [],
          imageUrls: taskData.imageUrl ? [taskData.imageUrl] : [],
          buttons: taskData.buttons || [],
          progress: taskData.progress,
          finishTime: taskData.finishTime,
          status: taskData.status
        }
      });
    } else {
      res.status(500).json({
        success: false,
        error: result.error || 'Midjourney动作执行失败'
      });
    }

  } catch (error) {
    console.error('❌ Midjourney动作执行失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 图生图 (Blend)
router.post('/blend', async (req, res) => {
  try {
    const { base64Array, options = {} } = req.body;

    if (!base64Array || !Array.isArray(base64Array) || base64Array.length < 2) {
      return res.status(400).json({
        success: false,
        error: '需要至少2张图片的base64数据'
      });
    }

    console.log('🎨 开始Midjourney图片混合');

    // 调用Blend API
    const result = await midjourneyService.blendImages(base64Array, options);

    if (result.success) {
      res.json({
        success: true,
        message: 'Midjourney图片混合完成',
        data: {
          taskId: result.taskId,
          images: result.images,
          imageUrls: result.imageUrls,
          buttons: result.buttons,
          finishTime: result.finishTime,
          status: result.status
        }
      });
    } else {
      res.status(500).json({
        success: false,
        error: result.error || 'Midjourney图片混合失败'
      });
    }

  } catch (error) {
    console.error('❌ Midjourney图片混合失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 图生文 (Describe)
router.post('/describe', async (req, res) => {
  try {
    const { base64 } = req.body;

    if (!base64) {
      return res.status(400).json({
        success: false,
        error: '需要图片的base64数据'
      });
    }

    console.log('🎨 开始Midjourney图片描述');

    // 调用Describe API
    const result = await midjourneyService.describeImage(base64);

    if (result.success) {
      res.json({
        success: true,
        message: 'Midjourney图片描述完成',
        data: {
          taskId: result.taskId,
          description: result.description,
          finishTime: result.finishTime,
          status: result.status
        }
      });
    } else {
      res.status(500).json({
        success: false,
        error: result.error || 'Midjourney图片描述失败'
      });
    }

  } catch (error) {
    console.error('❌ Midjourney图片描述失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 查询任务状态
router.get('/task/:taskId', async (req, res) => {
  try {
    const { taskId } = req.params;

    if (!taskId) {
      return res.status(400).json({
        success: false,
        error: '任务ID不能为空'
      });
    }

    console.log(`🔍 查询Midjourney任务状态: ${taskId}`);

    // 查询任务状态
    const taskData = await midjourneyService.getTaskStatus(taskId);

    res.json({
      success: true,
      data: taskData
    });

  } catch (error) {
    console.error('❌ 查询Midjourney任务状态失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 获取生成历史
router.get('/history', async (req, res) => {
  try {
    const { limit = 50 } = req.query;
    const result = await midjourneyService.getHistory(parseInt(limit));

    res.json({
      success: true,
      history: result.history,
      total: result.total
    });
  } catch (error) {
    console.error('❌ 获取Midjourney历史失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 获取本地图片
router.get('/images/:filename', async (req, res) => {
  try {
    const filename = req.params.filename;
    const path = require('path');
    const fs = require('fs').promises;

    const imagePath = path.join(midjourneyService.imagesDir, filename);

    // 检查文件是否存在
    await fs.access(imagePath);

    // 设置正确的Content-Type
    res.setHeader('Content-Type', 'image/png');

    // 发送文件
    res.sendFile(imagePath);

  } catch (error) {
    console.error('❌ 获取图片失败:', error);
    res.status(404).json({
      success: false,
      error: '图片不存在'
    });
  }
});

// 获取配置信息
router.get('/config', async (req, res) => {
  try {
    const status = await midjourneyService.checkMidjourneyStatus();
    
    res.json({
      success: true,
      config: {
        service: 'Midjourney',
        description: '通过Discord API与Midjourney Bot交互生成AI图片',
        features: [
          '🎨 AI图片生成',
          '🔄 图片放大和变体',
          '⚙️ 多种生成参数',
          '📊 生成历史记录',
          '🎯 高质量艺术风格',
          '🚀 异步任务处理'
        ],
        parameters: {
          prompt: '图片描述提示词',
          aspect_ratio: '宽高比 (如: 16:9, 1:1, 9:16)',
          quality: '质量等级 (0.25, 0.5, 1, 2)',
          stylize: '风格化程度 (0-1000)',
          chaos: '随机性 (0-100)',
          model: '模型版本 (5.2, 6等)'
        },
        status: status.configured ? '已配置' : '需要配置',
        requirements: [
          'Discord Bot Token',
          'Midjourney服务器频道ID',
          'Midjourney Bot权限'
        ]
      }
    });
  } catch (error) {
    console.error('❌ 获取Midjourney配置失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 下载图片
router.post('/download', async (req, res) => {
  try {
    const { imageUrl, filename } = req.body;
    
    if (!imageUrl) {
      return res.status(400).json({
        success: false,
        error: '图片URL不能为空'
      });
    }

    const savedPath = await midjourneyService.downloadImage(
      imageUrl, 
      filename || `midjourney_${Date.now()}.png`
    );
    
    res.json({
      success: true,
      message: '图片下载成功',
      path: savedPath,
      filename: filename || `midjourney_${Date.now()}.png`
    });

  } catch (error) {
    console.error('❌ Midjourney图片下载失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 测试连接
router.post('/test', async (req, res) => {
  try {
    const { token, channelId, serverId } = req.body;
    
    if (!token || !channelId) {
      return res.status(400).json({
        success: false,
        error: '缺少必要的配置参数'
      });
    }

    // 这里可以添加测试连接的逻辑
    // 暂时返回成功响应
    res.json({
      success: true,
      message: 'Midjourney连接测试成功',
      config: {
        hasToken: !!token,
        hasChannelId: !!channelId,
        hasServerId: !!serverId
      }
    });

  } catch (error) {
    console.error('❌ Midjourney连接测试失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

module.exports = router;
