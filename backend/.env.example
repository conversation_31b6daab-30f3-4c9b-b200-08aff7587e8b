# SCAIA 后端环境配置示例文件
# 复制此文件为 .env 并填入实际配置值

# ==================== 服务器配置 ====================
# 服务器端口号
PORT=3000

# 运行环境 (development, production, test)
NODE_ENV=development

# 前端应用地址（用于CORS配置）
FRONTEND_URL=http://localhost:3001

# ==================== 数据库配置 ====================
# MySQL 数据库连接配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=scaia_user
DB_PASSWORD=your_password_here
DB_NAME=scaia_db

# 数据库连接池配置
DB_CONNECTION_LIMIT=10
DB_ACQUIRE_TIMEOUT=60000
DB_TIMEOUT=60000

# ==================== JWT 配置 ====================
# JWT 密钥（请使用强密码）
JWT_SECRET=your_super_secret_jwt_key_here_please_change_this

# JWT 过期时间
JWT_EXPIRES_IN=24h

# 刷新令牌过期时间
JWT_REFRESH_EXPIRES_IN=7d

# ==================== 文件上传配置 ====================
# 上传文件存储目录
UPLOAD_DIR=./uploads

# 最大文件大小（字节）
MAX_FILE_SIZE=10485760

# 允许的图片文件类型
ALLOWED_IMAGE_TYPES=image/jpeg,image/png,image/gif,image/webp

# 允许的文档文件类型
ALLOWED_DOC_TYPES=application/pdf,text/plain,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document

# ==================== 日志配置 ====================
# 日志级别 (error, warn, info, debug)
LOG_LEVEL=info

# 日志文件目录
LOG_DIR=./logs

# 日志文件保留天数
LOG_MAX_FILES=30

# 单个日志文件最大大小
LOG_MAX_SIZE=20m

# ==================== Redis 配置（可选） ====================
# Redis 连接配置（用于会话存储和缓存）
# REDIS_HOST=localhost
# REDIS_PORT=6379
# REDIS_PASSWORD=
# REDIS_DB=0

# ==================== 邮件配置（可选） ====================
# SMTP 邮件服务配置
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_SECURE=false
# SMTP_USER=<EMAIL>
# SMTP_PASS=your_app_password
# <AUTHOR> <EMAIL>

# ==================== AI 服务配置（可选） ====================
# OpenAI API 配置
# OPENAI_API_KEY=your_openai_api_key_here
# OPENAI_API_BASE=https://api.openai.com/v1
# OPENAI_MODEL=gpt-4

# Midjourney API 配置
MIDJOURNEY_BASE_URL=https://api.gpt-best.com
MIDJOURNEY_API_KEY=sk-your-midjourney-api-key-here

# ==================== 安全配置 ====================
# 密码加密轮数
BCRYPT_ROUNDS=12

# 会话密钥
SESSION_SECRET=your_session_secret_here

# CORS 允许的域名（生产环境）
# ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# ==================== 速率限制配置 ====================
# API 请求速率限制（每15分钟）
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000

# 认证请求速率限制（每15分钟）
AUTH_RATE_LIMIT_MAX_REQUESTS=10

# 文件上传速率限制（每小时）
UPLOAD_RATE_LIMIT_WINDOW_MS=3600000
UPLOAD_RATE_LIMIT_MAX_REQUESTS=50

# ==================== 备份配置（可选） ====================
# 数据库备份目录
# BACKUP_DIR=./backups

# 自动备份间隔（小时）
# AUTO_BACKUP_INTERVAL=24

# 备份文件保留天数
# BACKUP_RETENTION_DAYS=30