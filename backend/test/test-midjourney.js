/**
 * Midjourney API 测试脚本
 * 
 * 使用方法：
 * 1. 确保已配置环境变量 MIDJOURNEY_API_KEY 和 MIDJOURNEY_BASE_URL
 * 2. 运行: node test/test-midjourney.js
 */

require('dotenv').config();
const AiHuituService = require('../services/aiHuituService');

async function testMidjourneyAPI() {
  console.log('🧪 开始测试 Midjourney API...');
  
  // 创建服务实例
  const service = new AiHuituService();
  
  try {
    // 测试配置
    console.log('\n📋 检查配置:');
    console.log('Base URL:', process.env.MIDJOURNEY_BASE_URL || 'https://api.gpt-best.com');
    console.log('API Key:', process.env.MIDJOURNEY_API_KEY ? '已配置' : '未配置');
    process.env.MIDJOURNEY_API_KEY = 'JEN1A9jQj7mBeWSP5lMUpTvSHaZ3lw==';

    // if (!process.env.MIDJOURNEY_API_KEY || process.env.MIDJOURNEY_API_KEY === 'JEN1A9jQj7mBeWSP5lMUpTvSHaZ3lw==') {
    //   console.log('❌ 请先配置 MIDJOURNEY_API_KEY 环境变量');
    //   return;
    // }
    
    // 测试简单的文生图
    console.log('\n🎨 测试文生图功能...');
    const prompt = 'a cute cat sitting on a chair, digital art';
    const options = {
      version: '6.1',
      aspect_ratio: '1:1'
    };
    
    console.log(`提示词: ${prompt}`);
    console.log(`参数: ${JSON.stringify(options)}`);
    
    const result = await service.generateImage(prompt, options);
    
    if (result.success) {
      console.log('✅ 文生图测试成功!');
      console.log('任务ID:', result.taskId);
      console.log('图片URL:', result.imageUrls);
      console.log('按钮:', result.buttons?.length || 0, '个');
      console.log('本地图片:', result.images?.length || 0, '张');
    } else {
      console.log('❌ 文生图测试失败:', result.error);
    }
    
    // 测试历史记录
    console.log('\n📚 测试历史记录功能...');
    const history = await service.getHistory(5);
    
    if (history.success) {
      console.log('✅ 历史记录测试成功!');
      console.log('历史记录数量:', history.total);
      console.log('最近记录:', history.history.slice(0, 2).map(h => ({
        id: h.id,
        prompt: h.prompt?.substring(0, 50) + '...',
        success: h.success,
        timestamp: h.timestamp
      })));
    } else {
      console.log('❌ 历史记录测试失败');
    }
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
    console.error('错误详情:', error);
  } finally {
    await service.close();
    console.log('\n🔌 测试完成，服务已关闭');
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  testMidjourneyAPI().catch(console.error);
}

module.exports = { testMidjourneyAPI };
